const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');
const https = require('https');
const http = require('http');

const USERNAME = 'beiming520';
const PASSWORD = '20000722zhou';
const LOGIN_URL = 'http://www.o666.net:800/UserCenter/';
const DOWNLOAD_DIR = path.join(__dirname, 'images');

if (!fs.existsSync(DOWNLOAD_DIR)) {
    fs.mkdirSync(DOWNLOAD_DIR);
}

function downloadImage(url, filepath) {
    return new Promise((resolve, reject) => {
        const mod = url.startsWith('https') ? https : http;
        mod.get(url, (res) => {
            if (res.statusCode === 200) {
                const fileStream = fs.createWriteStream(filepath);
                res.pipe(fileStream);
                fileStream.on('finish', () => {
                    fileStream.close(resolve);
                });
            } else {
                res.resume();
                reject(new Error(`Request Failed With Status Code: ${res.statusCode}`));
            }
        }).on('error', reject);
    });
}

(async () => {
    const browser = await puppeteer.launch({ headless: false }); // 可改为true为无头模式
    const page = await browser.newPage();
    await page.goto(LOGIN_URL, { waitUntil: 'networkidle2' });

    // 填写账号密码并登录（根据实际页面结构调整选择器）
    await page.type('input[name="username"],input[type="text"]', USERNAME);
    await page.type('input[name="password"],input[type="password"]', PASSWORD);
    await Promise.all([
        page.click('button[type="submit"],input[type="submit"],.login-btn'), // 根据实际按钮选择器调整
        page.waitForNavigation({ waitUntil: 'networkidle2' }),
    ]);

    // 进入S2区（根据实际页面结构调整点击方式）
    // 假设有个链接或按钮包含“S2区”字样
    await page.waitForSelector('a,button');
    const s2Link = await page.$x("//a[contains(text(),'S2') or contains(text(),'S2区')]");
    if (s2Link.length > 0) {
        await Promise.all([
            s2Link[0].click(),
            page.waitForNavigation({ waitUntil: 'networkidle2' }),
        ]);
    } else {
        console.log('未找到S2区入口，请手动检查页面结构。');
        await browser.close();
        return;
    }

    // 滚动页面，确保图片全部加载
    let previousHeight;
    try {
        while (true) {
            previousHeight = await page.evaluate('document.body.scrollHeight');
            await page.evaluate('window.scrollTo(0, document.body.scrollHeight)');
            await page.waitForTimeout(1000);
            const newHeight = await page.evaluate('document.body.scrollHeight');
            if (newHeight === previousHeight) break;
        }
    } catch (e) {}

    // 抓取所有图片链接
    const imgUrls = await page.evaluate(() => {
        const imgs = Array.from(document.querySelectorAll('img'));
        return imgs.map(img => img.src).filter(src => src && (src.startsWith('http') || src.startsWith('//')));
    });

    console.log(`共发现图片：${imgUrls.length} 张，开始下载...`);

    // 下载图片
    for (let i = 0; i < imgUrls.length; i++) {
        let url = imgUrls[i];
        if (url.startsWith('//')) url = 'http:' + url;
        const ext = path.extname(url).split('?')[0] || '.jpg';
        const filename = `img_${i + 1}${ext}`;
        const filepath = path.join(DOWNLOAD_DIR, filename);
        try {
            await downloadImage(url, filepath);
            console.log(`下载成功: ${filename}`);
        } catch (e) {
            console.log(`下载失败: ${filename}，原因: ${e.message}`);
        }
    }

    await browser.close();
    console.log('全部图片下载完成！');
})();